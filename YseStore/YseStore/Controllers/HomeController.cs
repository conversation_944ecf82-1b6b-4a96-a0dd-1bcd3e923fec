using Entitys;
using Microsoft.Extensions.Localization;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System.Text;
using YseStore.Common;
using YseStore.Common.Helper;
using YseStore.IService;
using YseStore.IService.Products;
using YseStore.IService.Sales;
using YseStore.IService.SiteSystem;
using YseStore.IService.Store;
using YseStore.IService.Visual;
using YseStore.Model.Response.Store;
using YseStore.Model.Response.Products;
using YseStore.Model.VM.Sales;
using YseStore.Models;
using YseStore.Service.Visual;

namespace YseStore.Controllers
{
    public class HomeController : BaseController
    {
        private readonly IStringLocalizer<HomeController> T;

        public IUserRoleServices UserRoleServices { get; }
        public IUrlService _urlService { get; }
        private IOpreationActivitiesService _opreationActivitiesService;
        private readonly IConfigService _config;
        private readonly ILogger<HomeController> _logger;
        private readonly ISalesAreasService _salesAreasService;
        private readonly ICurrencyService _currencyService;
        private readonly ILanguageServices _languageServices;
        private readonly IHomeProductService _homeProductService;
        private readonly IVisualPageBuilderService _visualPageBuilderService;
        private readonly IVisualHelpsService _visualHelpsService;
        private readonly IVisualProductService _visualProductService;

        public HomeController(IStringLocalizer<HomeController> localizer, IUserRoleServices userRoleServices,
            ILogger<HomeController> logger,
            IUrlService urlService, IOpreationActivitiesService opreationActivitiesService,
            IConfigService configService, ISalesAreasService salesAreasService, ICurrencyService currencyService,
            ILanguageServices languageServices, IMenuService menuService, IHomeProductService homeProductService,
            IVisualPageBuilderService visualPageBuilderService,
            IVisualHelpsService visualHelpsService, IVisualProductService visualProductService) : base(menuService)
        {
            T = localizer;
            UserRoleServices = userRoleServices;
            _logger = logger;
            _urlService = urlService;
            _opreationActivitiesService = opreationActivitiesService;
            _config = configService;
            _salesAreasService = salesAreasService;
            _currencyService = currencyService;
            _languageServices = languageServices;
            _homeProductService = homeProductService;
            _visualPageBuilderService = visualPageBuilderService;
            _visualHelpsService = visualHelpsService;
            _visualProductService = visualProductService;
        }

        //[Authorize]
        //[ResponseCache(Duration = 60)]
        public async Task<IActionResult> Index()
        {
            var lang = T["web.lang"];
            var mxlang = T;
            // UserRoleServices.SaveUserRole(0, 0);
            //var operateData=await _opreationActivitiesService.GetOperActiveData();
            string js = await OperationActivitiesInit();

            // 获取首页产品数据
            var homeProducts = await GetHomeProductsData();

            // 获取可视化页面数据
            var visualPageData = await _visualPageBuilderService.BuildVisualPageAsync("index");

            // 处理可视化产品数据
            var visualProductBlocks = new List<VisualProductBlockResponse>();
            var pluginsList = visualPageData.Plugins.Where(x => x.Type == "products").ToList();

            foreach (var plugin in pluginsList)
            {
                if (!string.IsNullOrEmpty(plugin.Settings))
                {
                    var productBlock = await _visualProductService.GetVisualProductsAsync(
                        plugin.Settings,
                        CurrentCurrency,
                        CurrentUserId);
                    visualProductBlocks.Add(productBlock);
                }
            }

            // 处理博客数据
            var settings = visualPageData?.PluginsByType
                ?.GetValueOrDefault("blog") // 安全获取字典值
                ?.FirstOrDefault()               // 安全获取第一个元素
                ?.Settings;                      // 设置

            JObject settingsDynamic = JObject.Parse(settings);
            var blogList = _visualHelpsService.GetBlogList(settingsDynamic?["Blog"].ToString(),6);

            return View(new
            {
                Mxlang = mxlang,
                OperateData = "",
                Js = js,
                HomeProducts = homeProducts,
                VisualProductBlocks = visualProductBlocks,
                blogList
            });
        }

        public IActionResult Privacy()
        {
            var lang = T["web.lang"];
            var mxlang = T;
            // UserRoleServices.SaveUserRole(0, 0);
            return View(mxlang);
        }

        [ResponseCache(Duration = 0, Location = ResponseCacheLocation.None, NoStore = true)]
        public IActionResult Error()
        {
            return View(new ErrorViewModel
                { RequestId = System.Diagnostics.Activity.Current?.Id ?? HttpContext.TraceIdentifier });
        }

        #region 初始化弹窗

        public async Task<string> OperationActivitiesInit()
        {
            try
            {
                var httpContext = HttpContext;
                var currenttime = (int)DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                // 1. 后台可视化不显示
                if (httpContext.Request.Query["editMode"] == "true") return string.Empty;

                // 2. 初始化Session数据
                var operActive = new OperActiveSession();
                if (HttpContext.Session.TryGetValue("operActive", out byte[] operActiveBytes) &&
                    operActiveBytes.Length > 0)
                {
                    string operActiveStr = Encoding.UTF8.GetString(operActiveBytes);
                    operActive = JsonConvert.DeserializeObject<OperActiveSession>(operActiveStr);
                }

                var editTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                var config = await _config.GetConfigByGroup("global", "operation_activities_edit_time");
                if (config != null && !string.IsNullOrEmpty(config.Value))
                {
                    editTime = int.Parse(config.Value);
                }

                // 3. 处理编辑时间变更
                if (operActive.EditTime != editTime)
                {
                    operActive = new OperActiveSession { EditTime = Convert.ToInt32(editTime) };
                }

                // 4. 记录进入网站时间
                if (operActive.EnterWebTime == 0)
                {
                    operActive.EnterWebTime = (int)DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                }

                // 5. 查询有效活动
                if (operActive.Data?.OperaData == null || !operActive.Data.OperaData.Any())
                {
                    operActive.Data = new OperationSessionData();
                    var activities = await _opreationActivitiesService.GetAllOperActiveData();


                    foreach (var activity in activities)
                    {
                        // 分类映射
                        if (!operActive.Data.CategoryMap.ContainsKey(activity.Category))
                        {
                            operActive.Data.CategoryMap[activity.Category] = new List<int>();
                        }

                        operActive.Data.CategoryMap[activity.Category].Add(activity.OId);

                        // 活动详细数据
                        operActive.Data.Activities[activity.OId] = activity;

                        // 按显示时间分组
                        if (!operActive.Data.OperaData.ContainsKey(activity.ShowTime))
                        {
                            operActive.Data.OperaData[activity.ShowTime] = new List<int>();
                        }

                        operActive.Data.OperaData[activity.ShowTime].Add(activity.OId);
                    }
                }


                // 保存更新后的Session
                httpContext.Session.SetString("operActive", operActive.ToJson());

                // 7. 处理Cookie
                var cookieSaveTime = 3600 * 24 * 7; // 7天
                var cookieOperActive = new Dictionary<string, int>();

                if (httpContext.Request.Cookies.TryGetValue("operActive", out var cookieValue))
                {
                    cookieOperActive = JsonConvert.DeserializeObject<Dictionary<string, int>>(cookieValue);

                    // 清理过期项
                    var expiredKeys = cookieOperActive
                        .Where(kvp => kvp.Value + cookieSaveTime < currenttime)
                        .Select(kvp => kvp.Key)
                        .ToList();

                    foreach (var key in expiredKeys)
                    {
                        cookieOperActive.Remove(key);
                    }
                }

                // 设置新Cookie
                var cookieOptions = new CookieOptions
                {
                    Expires = DateTimeOffset.FromUnixTimeSeconds(currenttime + cookieSaveTime),
                    Path = "/",
                    HttpOnly = true,
                    Secure = httpContext.Request.IsHttps
                };

                httpContext.Response.Cookies.Append("operActive",
                    JsonConvert.SerializeObject(cookieOperActive), cookieOptions);

                // 8. 输出前端JS代码
                if (operActive.Data?.OperaData != null && operActive.Data.OperaData.Any())
                {
                    var jsScript = new StringBuilder();
                    //jsScript.AppendLine("<script>");
                    jsScript.AppendLine(
                        "var $enterTime = parseInt(parseInt(new Date().getTime()).toString().substring(0, parseInt(new Date().getTime()).toString().length -3));");
                    jsScript.AppendLine(
                        "if(!temporaryStorage.getItem(\"operActive\",\"enterWebTime\")) { sessionStorage.setItem(\"operActive\", \"{\\\"enterWebTime\\\" : \" + $enterTime + \"}\");}");
                    jsScript.AppendLine(
                        "window.enterTime = temporaryStorage.getItem(\"operActive\",\"enterWebTime\"); ");

                    // 序列化并转义Session数据
                    var operateData = await _opreationActivitiesService.GetOperActiveData();
                    //var sessionData = JsonConvert.SerializeObject(operActive.Data);
                    //var escapedData = JsonConvert.ToString(sessionData).Trim('"');
                    jsScript.AppendLine($"window.operActiveData = '{operateData.ToJson()}';");

                    jsScript.AppendLine("window.operActivePassData = temporaryStorage.getItem('operActive','pass');");
                    // jsScript.AppendLine("</script>");
                    return jsScript.ToString();
                }

                return string.Empty;
            }
            catch (Exception ex)
            {
                // 记录异常以便调试
                Console.WriteLine($"弹窗初始化出错: {ex.Message}");
                Console.WriteLine($"异常堆栈: {ex.StackTrace}");

                return string.Empty;
            }
        }

        #endregion


        /// <summary>
        /// 错误链接
        /// </summary>
        /// <param name="code"></param>
        /// <returns></returns>
        [Route("error/{code:int}")]
        public IActionResult Error(int code)
        {
            ViewData["ErrorCode"] = code;


            return View("Error");
        }


        /// <summary>
        /// 错误页面
        /// </summary>
        /// <returns></returns>
        public IActionResult ErrorPage()
        {
            var page = HttpContext.Items["PageData"] as JObject;

            ViewData["ErrorCode"] = page["Code"].ObjToInt();

            return View("Error");
        }


        #region header

        /// <summary>
        /// 销售分区下的货币和语言
        /// </summary>
        /// <returns></returns>
        public async Task<IActionResult> SaleAreaCurrencyAndLang()
        {
            var saleAreasConfig = await _config.GetConfigByGroup("global", "SalesAreasStatus");

            sales_areas area = new sales_areas();

            if (saleAreasConfig != null && saleAreasConfig.Value == "1")
            {
                // 获取国家分区数据
                area = await _salesAreasService.GetSalesAreasInfoCache(CurrentSalesAreaCode);
            }
            else //默认数据
            {
                area.Counntries = new List<string>();
                area.CountryList = new List<country>();

                //获取币种和语言
                var usedCurrency = await _currencyService.GetUsedCurrencyCache();
                //前台展示的币种
                var currency = usedCurrency.Where(c => c.IsWebShow).ToList();

                area.Currencies = currency.Select(c => c.Currency).ToList();
                area.CurrencyList = currency;


                //
                var lang = await _languageServices.GetUsedLangsCache();
                //前台展示的语言
                lang = lang.Where(c => c.Status == true).ToList();
                area.Languages = lang.Select(c => c.Language).ToList();
                area.LanguageList = lang;
            }

            //是否显示国家
            if (area.Currencies.Count == 1 && area.Currencies.FirstOrDefault() == CurrentCurrency)
            {
                ViewData["currencyShow"] = "0";
            }
            else
            {
                ViewData["currencyShow"] = "1";
            }

            //是否显示语言
            if (area.Languages.Count == 1 && area.Languages.FirstOrDefault() == CurrentLang)
            {
                ViewData["languageShow"] = "0";
            }
            else
            {
                ViewData["languageShow"] = "1";
            }


            ViewData["currency"] = CurrentCurrency;
            ViewData["language"] = CurrentLang;

            return PartialView("SaleArea_Lang_Currency", area);
        }

        /// <summary>
        /// 通过HTMX加载导航菜单数据
        /// </summary>
        /// <returns></returns>
        public async Task<IActionResult> NavigationMenus()
        {
            try
            {
                // 检查_menuService是否为null
                if (_menuService == null)
                {
                    return Content("<div>MenuService not available</div>");
                }


                // 获取导航菜单数据
                var navMenus = await _menuService.GetNavMenusAsync(CurrentLang);


                // 获取底部导航菜单数据
                var footerNavMenus = await _menuService.GetFooterNavMenusAsync(CurrentLang);


                var menuData = new
                {
                    NavMenus = navMenus ?? new List<MenuResponse>(),
                    FooterNavMenus = footerNavMenus ?? new List<MenuResponse>()
                };

                return PartialView("NavigationMenus", menuData);
            }
            catch (Exception ex)
            {
                if (ex.InnerException != null)
                {
                }

                // 返回简单的HTML内容而不是StatusCode
                return Content($"<div>Error loading navigation menus: {ex.Message}</div>");
            }
        }

        /// <summary>
        /// 通过HTMX加载底部菜单数据
        /// </summary>
        /// <returns></returns>
        public async Task<IActionResult> FooterMenus()
        {
            try
            {
                // 检查_menuService是否为null
                if (_menuService == null)
                {
                    return Content("<div>MenuService not available</div>");
                }


                // 获取底部导航菜单数据
                var footerNavMenus = await _menuService.GetFooterNavMenusAsync(CurrentLang);

                var menuData = new
                {
                    FooterNavMenus = footerNavMenus ?? new List<MenuResponse>()
                };

                return PartialView("FooterMenus", menuData);
            }
            catch (Exception ex)
            {
                if (ex.InnerException != null)
                {
                }

                // 返回简单的HTML内容而不是StatusCode
                return Content($"<div>Error loading footer menus: {ex.Message}</div>");
            }
        }

        #endregion

        #region 国家分区

        /// <summary>
        /// 国家分区
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("/select/location")]
        public async Task<IActionResult> SaleArea(string jumpUrl)
        {
            // 获取国家分区数据
            var list = await _salesAreasService.GetSalesAreasCache();

            // 获取当前销售区域代码
            ViewData["SaleAreaCode"] = CurrentSalesAreaCode;
            ViewData["jumpUrl"] = jumpUrl;

            return View(list);
        }

        #endregion

        #region 首页产品数据

        /// <summary>
        /// 获取首页产品数据
        /// </summary>
        /// <returns></returns>
        private async Task<object> GetHomeProductsData()
        {
            try
            {
                // 定义首页产品URL列表（从页面中提取的所有静态数据）
                var productUrls = new List<string>
                {
                    // Guía Turística Sistema（12个产品）
                    "sistema-tourguide-para-guias-turisticos",
                    "tt127-audioguia-y-radioguia-2-en-1-sistema",
                    "tt106-sistema-guia-turistico-inalambrico-caja-carga",
                    "t130p-t131p-sistema-de-comunicacion-inalambrico-portatil",
                    "tt116-sistema-de-guias-de-viaje-para-visitas-a-la-planta",
                    "tt126-tt126r-sistema-de-comunicacion-bidireccional",
                    "tt129-sistema-de-comunicacion-full-duplex-inalambrico",
                    "t130s-t131s-sistemas-de-guia-de-audio",
                    "tt122-sistema-de-visitas-guiadas",
                    "t130u-t131u-radioguia-antiinterferencias-guia-grupal",
                    "tt126-sistema-de-guia-bidireccional-para-visitas-a-fabricas-turismo-escuelas-y-conferencias",
                    "tt128-sistema-de-audioguia",


                    // Sistema de Buscapersonas（12个产品）
                    "retekess-td177-sistema-de-paginacion-matricial",
                    "td156-sistema-de-llamadas-inalambrico",
                    "retekess-163-buscarpersonas-para-restaurante",
                    "td175p-sistema-de-localizacion-para-restaurantes",
                    "retekess-td168r-sistema-de-llamadas-inalambrico",
                    "td166-sistema-de-buscapersonas-inalambrico-para-almacenes",
                    "td157-buscapersonas-sistema-de-llamadas-inalambrico",
                    "td183-avisadores-de-clientes-de-largo-alcance",
                    "td165-sistema-de-llamadas-inalambricas",
                    "td161-paging-system-negra",
                    "retekess-buscapersonas-inalambrico-td168r-con-2-teclados-independientes",
                    "t112-avisadores-de-clientes",


                    // Presione para Servicio（12个产品）
                    "retekess-td154-sistema-inalambrico-de-llamada-a-camarero-de-restaurante",
                    "retekess-td005-boton-de-llamada-multiboton-y-td112-reloj-de-pulsera-con-pantalla",
                    "retekess-sistema-de-llamadas-inalambricas-para-restaurantes-td113-receptor-de-reloj-con-teclado-td029",
                    "t128-sistema-de-llamadas-inalambricas-con-boton-de-llamada-t117",
                    "retekess-td112-reloj-receptor-con-boton-de-llamada-td036",
                    "receptor-td136-boton-td019",
                    "t114-receptor-de-pantalla-td003-transmisor-de-boton-de-llamada",
                    "retekess-sistema-de-llamada-a-restaurante-td113-watch-con-boton-de-llamada-t117",
                    "retekess-2000m-sistema-de-llamada-a-enfermera-th108",
                    "sistema-de-llamadas-inalambricas-t128-con-boton-de-llamada-td004",
                    "retekess-td105-sistema-de-llamada-de-mesa-de-restaurante",
                    "retekess-sistema-de-llamada-a-enfermera-th107-para-hospital-clinica-hogar-de-ancianos"
                };

                // 调用服务获取产品数据
                var homeProducts =
                    await _homeProductService.GetHomeProductsAsync(productUrls, CurrentCurrency, CurrentUserId);
                return homeProducts;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"获取首页产品数据时出错: {ex.Message}");
                Console.WriteLine($"异常堆栈: {ex.StackTrace}");

                // 返回空的 HomeProductsResponse 对象而不是匿名对象
                return new Model.Response.Products.HomeProductsResponse();
            }
        }

        #endregion
    }
}