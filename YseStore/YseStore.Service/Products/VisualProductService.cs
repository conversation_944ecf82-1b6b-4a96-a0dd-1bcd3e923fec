using Microsoft.Extensions.Logging;
using SqlSugar;
using YseStore.Common.Cache;
using YseStore.IService.Products;
using YseStore.Model.Response.Products;
using Entitys;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using YseStore.IService;
using YseStore.IService.Sales;
using YseStore.Common.Helper;

namespace YseStore.Service.Products
{
    /// <summary>
    /// 可视化产品服务实现
    /// </summary>
    public class VisualProductService : IVisualProductService
    {
        private readonly ILogger<VisualProductService> _logger;
        private readonly ISqlSugarClient _db;
        private readonly ICaching _caching;
        private readonly ICurrencyService _currencyService;
        private readonly IFlashSaleService _flashSaleService;

        public VisualProductService(
            ILogger<VisualProductService> logger,
            ISqlSugarClient db,
            ICaching caching,
            ICurrencyService currencyService,
            IFlashSaleService flashSaleService)
        {
            _logger = logger;
            _db = db;
            _caching = caching;
            _currencyService = currencyService;
            _flashSaleService = flashSaleService;
        }

        /// <summary>
        /// 根据可视化插件获取产品数据
        /// </summary>
        public async Task<VisualProductBlockResponse> GetVisualProductsAsync(visual_plugins plugin, string currentCurrency = "", int userId = 0)
        {
            try
            {
                if (plugin == null || string.IsNullOrEmpty(plugin.Settings))
                {
                    return new VisualProductBlockResponse();
                }

                // 解析JSON设置
                var settings = JObject.Parse(plugin.Settings);

                var response = new VisualProductBlockResponse
                {
                    PId = plugin.PId,
                    Type = plugin.Type ?? string.Empty,
                    Mode = plugin.Mode ?? string.Empty,
                    Title = settings["Title"]?.ToString() ?? string.Empty,
                    SubTitle = settings["SubTitle"]?.ToString() ?? string.Empty,
                    LinkName = settings["LinkName"]?.ToString() ?? string.Empty,
                    Link = settings["Link"]?.ToString() ?? string.Empty
                };

                var productsSettings = settings["Products"];
                if (productsSettings == null)
                {
                    return response;
                }

                var productType = productsSettings["type"]?.ToString();
                
                if (productType == "auto")
                {
                    // 自动模式
                    var categoryId = int.TryParse(productsSettings["cateid"]?.ToString(), out var cateId) ? cateId : 0;
                    var tagIdStr = productsSettings["tagid"]?.ToString() ?? string.Empty;
                    var sortType = productsSettings["more"]?.ToString() ?? "time_desc";
                    var number = int.TryParse(productsSettings["number"]?.ToString(), out var num) ? num : 10;

                    // 解析标签ID
                    var tagIds = new List<int>();
                    if (!string.IsNullOrEmpty(tagIdStr))
                    {
                        tagIds = tagIdStr.Split(',', StringSplitOptions.RemoveEmptyEntries)
                            .Select(id => int.TryParse(id.Trim(), out var tagId) ? tagId : 0)
                            .Where(id => id > 0)
                            .ToList();
                    }

                    response.Products = await GetProductsByAutoModeAsync(categoryId, tagIds, sortType, number, currentCurrency, userId);
                }
                else if (productType == "manual")
                {
                    // 手动模式
                    var productIdsArray = productsSettings["value"] as JArray;
                    if (productIdsArray != null)
                    {
                        var productIds = productIdsArray
                            .Select(id => int.TryParse(id.ToString(), out var prodId) ? prodId : 0)
                            .Where(id => id > 0)
                            .ToList();

                        response.Products = await GetProductsByManualModeAsync(productIds, currentCurrency, userId);
                    }
                }

                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取可视化产品数据时出错，插件ID: {PId}, 设置: {Settings}", plugin?.PId, plugin?.Settings);
                return new VisualProductBlockResponse
                {
                    PId = plugin?.PId ?? 0,
                    Type = plugin?.Type ?? string.Empty,
                    Mode = plugin?.Mode ?? string.Empty
                };
            }
        }

        /// <summary>
        /// 根据分类ID和标签ID获取产品列表（自动模式）
        /// </summary>
        public async Task<List<HomeProductItem>> GetProductsByAutoModeAsync(int categoryId, List<int> tagIds, string sortType, int number, string currentCurrency = "", int userId = 0)
        {
            try
            {
                var query = _db.Queryable<products>()
                    .Where(p => p.SoldOut != true); // 排除已下架产品

                // 如果指定了分类ID，通过products_category_relate表关联查询
                if (categoryId > 0)
                {
                    query = query.Where(p => SqlFunc.Subqueryable<products_category_relate>()
                        .Where(pcr => pcr.ProId == p.ProId && pcr.CateId == categoryId)
                        .Any());
                }

                // 如果指定了标签ID，过滤包含这些标签的产品
                if (tagIds != null && tagIds.Any())
                {
                    foreach (var tagId in tagIds)
                    {
                        var tagIdStr = $"|{tagId}|";
                        query = query.Where(p => p.Tags.Contains(tagIdStr));
                    }
                }

                // 排序


                // 限制数量
                var products = await query.Take(number).ToListAsync();

                return await BuildProductItems(products, currentCurrency, userId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取自动模式产品数据时出错，分类ID: {CategoryId}, 标签IDs: {TagIds}", categoryId, string.Join(",", tagIds ?? new List<int>()));
                return new List<HomeProductItem>();
            }
        }

        /// <summary>
        /// 根据产品ID列表获取产品列表（手动模式）
        /// </summary>
        public async Task<List<HomeProductItem>> GetProductsByManualModeAsync(List<int> productIds, string currentCurrency = "", int userId = 0)
        {
            try
            {
                if (productIds == null || !productIds.Any())
                {
                    return new List<HomeProductItem>();
                }

                var products = await _db.Queryable<products>()
                    .Where(p => productIds.Contains(p.ProId) && p.SoldOut != true)
                    .ToListAsync();

                // 按照传入的ID顺序排序
                var orderedProducts = productIds
                    .Select(id => products.FirstOrDefault(p => p.ProId == id))
                    .Where(p => p != null)
                    .ToList();

                return await BuildProductItems(orderedProducts, currentCurrency, userId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取手动模式产品数据时出错，产品IDs: {ProductIds}", string.Join(",", productIds ?? new List<int>()));
                return new List<HomeProductItem>();
            }
        }

        /// <summary>
        /// 构建产品项目列表
        /// </summary>
        private async Task<List<HomeProductItem>> BuildProductItems(List<products> products, string currentCurrency, int userId)
        {
            if (!products.Any())
            {
                return new List<HomeProductItem>();
            }

            var productIds = products.Select(p => p.ProId).ToList();

            // 获取产品图片
            var productImages = await _db.Queryable<products_images>()
                .Where(img => productIds.Contains(img.ProId))
                .OrderBy(img => img.Position)
                .ToListAsync();

            // 获取用户收藏状态
            var userFavorites = new HashSet<int>();
            if (userId > 0)
            {
                var favoriteProductIds = await _db.Queryable<user_favorite>()
                    .Where(f => f.UserId == userId && productIds.Contains(f.ProId.Value))
                    .Select(f => f.ProId.Value)
                    .ToListAsync();
                userFavorites = favoriteProductIds.ToHashSet();
            }

            var result = new List<HomeProductItem>();

            foreach (var product in products)
            {
                try
                {
                    // 获取产品价格信息
                    var priceInfo = await CalculatePriceWithPromotion(product, currentCurrency);

                    var productImages_single = productImages.Where(img => img.ProId == product.ProId).ToList();

                    var item = new HomeProductItem
                    {
                        ProductId = product.ProId,
                        ProductName = product.Name_en,
                        ProductUrl = $"/products/{product.PageUrl}",
                        PrimaryImage = productImages_single.FirstOrDefault(img => img.Position == 1)?.PicPath ?? product.PicPath_0,
                        HoverImage = productImages_single.FirstOrDefault(img => img.Position == 2)?.PicPath,
                        Price = priceInfo.Price,
                        OldPrice = priceInfo.OldPrice,
                        PriceFormat = priceInfo.PriceFormat,
                        OldPriceFormat = priceInfo.OldPriceFormat,
                        SKU = product.SKU,
                        IsHot = IsHotProduct(product),
                        IsNew = IsNewProduct(product),
                        Rating = product.Rating ?? 0,
                        IsFavorited = userFavorites.Contains(product.ProId)
                    };

                    result.Add(item);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "构建产品项目时出错，产品ID: {ProductId}", product.ProId);
                }
            }

            return result;
        }

        /// <summary>
        /// 计算包含促销的价格信息 - 完全按照HomeProductService.cs逻辑实现
        /// </summary>
        private async Task<(decimal Price, decimal? OldPrice, string PriceFormat, string OldPriceFormat)>
            CalculatePriceWithPromotion(products product, string currentCurrency = "")
        {
            try
            {
                // 获取币种信息
                var userCurrency = await _currencyService.GetCurrency(currentCurrency);
                var manageCurrency = await _currencyService.GetManageDefaultCurrency();

                // 处理价格信息
                decimal promotionPrice = 0;
                string priceFormat = null;
                string originalPriceFormat = null;
                string promotionPriceFormat = null;

                // 1. 获取促销价格信息
                try
                {
                    var flashSaleResult = await _flashSaleService.FlashSaleCalculation(
                        product,
                        product.Price_1.HasValue ? product.Price_1.Value : 0,
                        product.Price_0.HasValue ? product.Price_0.Value : 0,
                        "", // variantsId
                        0 // userId
                    );

                    if (flashSaleResult != null && flashSaleResult.IsUsed == 1 && flashSaleResult.Price > 0)
                    {
                        promotionPrice = flashSaleResult.Price;
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "获取产品{ProductId}促销价格失败", product.ProId);
                    // 出错时促销价格保持为0
                }

                // 2. 格式化促销价格（只有当促销价格大于0时才显示）
                if (promotionPrice > 0)
                {
                    var promotionPriceResult =
                        _currencyService.ShowPriceFormat(promotionPrice, userCurrency, manageCurrency);
                    promotionPriceFormat = promotionPriceResult.Item2;
                }

                // 3. 正确的价格显示逻辑：原价始终不变，促销价作用在售价上
                decimal finalPrice;
                decimal? oldPrice = null;

                if (promotionPrice > 0)
                {
                    // 有促销价时：现价位置显示促销价，原价保持不变
                    priceFormat = promotionPriceFormat; // 现价位置显示促销价
                    finalPrice = promotionPrice;

                    // 原价位置显示真正的原价（Price_0）
                    if (product.Price_0.HasValue && product.Price_0.Value > 0)
                    {
                        var originalPriceResult =
                            _currencyService.ShowPriceFormat(product.Price_0.Value, userCurrency, manageCurrency);
                        originalPriceFormat = originalPriceResult.Item2;
                        oldPrice = product.Price_0.Value;
                    }
                }
                else
                {
                    // 没有促销价时：正常显示现价和原价
                    // 格式化销售价格
                    if (product.Price_1.HasValue)
                    {
                        var priceResult =
                            _currencyService.ShowPriceFormat(product.Price_1.Value, userCurrency, manageCurrency);
                        priceFormat = priceResult.Item2;
                        finalPrice = product.Price_1.Value;
                    }
                    else
                    {
                        finalPrice = 0;
                    }

                    // 如果原价大于售价，显示原价作为划线价
                    if (product.Price_0.HasValue && product.Price_0.Value > 0 &&
                        product.Price_1.HasValue && product.Price_0.Value > product.Price_1.Value)
                    {
                        var originalPriceResult =
                            _currencyService.ShowPriceFormat(product.Price_0.Value, userCurrency, manageCurrency);
                        originalPriceFormat = originalPriceResult.Item2;
                        oldPrice = product.Price_0.Value;
                    }
                }

                return (finalPrice, oldPrice, priceFormat ?? "", originalPriceFormat ?? "");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "计算产品{ProductId}价格时出错", product.ProId);

                // 出错时返回基础价格
                var basicPrice = product.Price_1.HasValue ? product.Price_1.Value : 0;
                return (basicPrice, null, FormatPrice(basicPrice), "");
            }
        }

        /// <summary>
        /// 格式化价格
        /// </summary>
        private string FormatPrice(decimal? price)
        {
            if (!price.HasValue || price <= 0)
            {
                return "";
            }

            return $"EUR{price:F2}";
        }

        /// <summary>
        /// 判断是否为热门产品
        /// </summary>
        private bool IsHotProduct(products product)
        {
            // 可以根据销量、评分等判断
            return product.Rating >= 4.5m || product.Tags?.Contains("Hot") == true;
        }

        /// <summary>
        /// 判断是否为新产品
        /// </summary>
        private bool IsNewProduct(products product)
        {
            // 判断产品是否在30天内创建
            var thirtyDaysAgo = DateTimeHelper.ConvertToUnixTimestamp(DateTime.Now.AddDays(-30));
            return product.AccTime >= thirtyDaysAgo || product.Tags?.Contains("New") == true;
        }
    }
}
