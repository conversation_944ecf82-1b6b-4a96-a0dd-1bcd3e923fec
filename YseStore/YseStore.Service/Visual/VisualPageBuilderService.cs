using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using YseStore.Common.Cache;
using YseStore.IService.Products;
using YseStore.IService.Visual;
using YseStore.Model.Response.Visual;

namespace YseStore.Service.Visual
{
    /// <summary>
    /// 可视化页面构建服务
    /// </summary>
    public class VisualPageBuilderService : IVisualPageBuilderService
    {
        private readonly IVisualDraftsService _visualDraftsService;
        private readonly IVisualProductService _visualProductService;
        private readonly IVisualHelpsService _visualHelpsService;
        private readonly IVisualTemplateServicee _visualTemplateService;
        private readonly IVisualPagesService _visualPagesService;
        private readonly IVisualPluginsService _visualPluginsService;
        private readonly ICaching _caching;
        private readonly ILogger<VisualPageBuilderService> _logger;

        public VisualPageBuilderService(
            IVisualDraftsService visualDraftsService,
            IVisualProductService visualProductService,
            IVisualTemplateServicee visualTemplateService,
            IVisualHelpsService visualHelpsService,
            IVisualPagesService visualPagesService,
            IVisualPluginsService visualPluginsService,
            ICaching caching,
            ILogger<VisualPageBuilderService> logger)
        {
            _visualDraftsService = visualDraftsService;
            _visualProductService = visualProductService;
            _visualTemplateService = visualTemplateService;
            _visualHelpsService = visualHelpsService;
            _visualPagesService = visualPagesService;
            _visualPluginsService = visualPluginsService;
            _caching = caching;
            _logger = logger;
        }

        /// <summary>
        /// 根据页面类型构建可视化页面数据
        /// </summary>
        /// <param name="pages">页面类型（如：index、products等）</param>
        /// <returns></returns>
        public async Task<VisualPageResponse> BuildVisualPageAsync(string pages, string CurrentCurrency = "",
            int CurrentUserId = 0)
        {
            try
            {
                var response = new VisualPageResponse();

                // 1. 获取最新的可见草稿
                var draft = await _visualDraftsService.GetLatestVisibleDraftAsync();
                if (draft == null)
                {
                    _logger.LogWarning("未找到可见的草稿数据");
                    return response;
                }

                // 2. 根据DraftsId和页面类型获取模板
                var template = await _visualTemplateService.GetTemplateByDraftsIdAndPagesAsync(draft.DraftsId, pages);
                if (template == null)
                {
                    // _logger.LogWarning("未找到对应的模板数据，DraftsId: {DraftsId}, Pages: {Pages}", draft.DraftsId, pages);
                    return response;
                }

                // 3. 根据TemplateId获取页面配置
                var page = await _visualPagesService.GetPagesByTemplateIdAsync(template.TemplateId);
                if (page == null)
                {
                    _logger.LogWarning("未找到对应的页面配置，TemplateId: {TemplateId}", template.TemplateId);
                    return response;
                }

                // 4. 解析插件ID数组并获取插件列表
                if (!string.IsNullOrEmpty(page.Plugins))
                {
                    try
                    {
                        var pluginIds = JsonConvert.DeserializeObject<List<int>>(page.Plugins);
                        if (pluginIds != null && pluginIds.Any())
                        {
                            var pluginsFromDb = await _visualPluginsService.GetPluginsByIdsAsync(pluginIds);

                            // 严格按照page.Plugins中的ID顺序排列插件
                            var plugins = pluginIds
                                .Select(id => pluginsFromDb.FirstOrDefault(p => p.PId == id))
                                .Where(p => p != null)
                                .ToList();

                            response.Plugins = plugins;
                            // 按Type分组插件
                            response.PluginsByType = plugins
                                .Where(p => !string.IsNullOrEmpty(p.Type))
                                .GroupBy(p => p.Type)
                                .ToDictionary(g => g.Key, g => g.ToList());
                            // 处理可视化产品数据
                            var pluginsList = response.Plugins.Where(x => x.Type == "products").ToList();

                            foreach (var plugin in pluginsList)
                            {
                                var productBlock = await _visualProductService.GetVisualProductsAsync(
                                    plugin,
                                    CurrentCurrency,
                                    CurrentUserId);
                                response.VisualProductBlock.Add(productBlock);
                            }
                            // 处理博客数据
                            var settings = response.PluginsByType
                                ?.GetValueOrDefault("blog") // 安全获取字典值
                                ?.FirstOrDefault() // 安全获取第一个元素
                                ?.Settings; // 设置
                            JObject settingsDynamic = JObject.Parse(settings);
                            response.BlogList = _visualHelpsService.GetBlogList(settingsDynamic?["Blog"].ToString(), 6);
                        }
                    }
                    catch (JsonException ex)
                    {
                        _logger.LogError(ex, "解析插件ID数组失败，Plugins: {Plugins}", page.Plugins);
                    }
                }

                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "构建可视化页面数据失败，Pages: {Pages}", pages);
                return new VisualPageResponse();
            }
        }

        /// <summary>
        /// 获取页面插件配置数据
        /// </summary>
        /// <param name="pages">页面类型</param>
        /// <returns></returns>
        public async Task<Dictionary<string, object>> GetPagePluginConfigAsync(string pages)
        {
            try
            {
                var cacheKey = $"visual_page_config_{pages}";

                // 尝试从缓存获取
                var cachedConfig = await _caching.GetAsync<Dictionary<string, object>>(cacheKey);
                if (cachedConfig != null)
                {
                    return cachedConfig;
                }

                // var visualPage = await BuildVisualPageAsync(pages);
                var config = new Dictionary<string, object>();

                // if (visualPage.Plugins.Any())
                // {
                //     config["plugins"] = visualPage.Plugins;
                //     config["pluginsByType"] = visualPage.PluginsByType;
                // }

                // 缓存配置数据（缓存10分钟）
                await _caching.SetAsync(cacheKey, config, TimeSpan.FromMinutes(10));

                return config;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取页面插件配置数据失败，Pages: {Pages}", pages);
                return new Dictionary<string, object>();
            }
        }
    }
}