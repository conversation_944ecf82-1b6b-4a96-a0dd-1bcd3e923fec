using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json.Linq;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using YseStore.Common;
using YseStore.Common.Cache;
using YseStore.IService.Visual;

namespace YseStore.Service.Visual
{
    /// <summary>
    /// 店铺装修插件
    /// </summary>
    public class VisualPluginsService : BaseServices<visual_plugins>, IVisualPluginsService
    {
        private readonly ISqlSugarClient db;
        private readonly ICaching _caching;
        private readonly ILogger<VisualPluginsService> _logger;
        private readonly IStringLocalizer<VisualPluginsService> T;

        public VisualPluginsService(ISqlSugarClient db, ICaching caching, ILogger<VisualPluginsService> logger, IStringLocalizer<VisualPluginsService> localizer)
        {
            this.db = db;
            _caching = caching;
            _logger = logger;
            T = localizer;
        }


        /// <summary>
        /// 读取插件JSON配置
        /// </summary>
        /// <param name="type">插件类型</param>
        /// <param name="mode"></param>
        /// <param name="drafts">插件风格</param>
        /// <returns></returns>
        public async Task<JObject> GetPluginsConfig(string type, string mode, visual_drafts drafts)
        {

            string config = await System.IO.File.ReadAllTextAsync(System.IO.Path.Combine(System.Environment.CurrentDirectory, $"wwwroot/template/default/cusvis_mode/{type}/{mode}/config.json"));
            if (!string.IsNullOrEmpty(config))
            {
                //配置
                var configModel = config.JsonToObj<JObject>();

                //themesConfig
                var themesConfig = await System.IO.File.ReadAllTextAsync(System.IO.Path.Combine(System.Environment.CurrentDirectory, $"wwwroot/template/default/inc/themes.json"));
                if (!themesConfig.IsNullOrEmpty())
                {
                    var themesConfigModel = themesConfig.JsonToObj<JObject>();

                    var pluginsExtConfig = themesConfigModel["PluginsExtConfig"][$"{type}-{mode}"];
                    if (pluginsExtConfig != null && !pluginsExtConfig.IsNullOrEmpty())
                    {
                        var obj = pluginsExtConfig.ToObject<JObject>();

                        configModel = ArrayMerge(configModel, obj);
                    }

                }


                return configModel;

            }

            return null;

        }

        /// <summary>
        /// 递归合并数组，并排除空值
        /// </summary>
        /// <param name="arrs"></param>
        /// <returns></returns>
        public static JObject ArrayMerge(params JObject[] jObjects)
        {
            JObject merged = new JObject();

            foreach (var jObject in jObjects)
            {
                if (jObject == null) continue;

                foreach (var property in jObject)
                {
                    var key = property.Key;
                    var value = property.Value;

                    if (value.Type == JTokenType.Object && merged[key] is JObject mergedObj)
                    {
                        // Recursively merge nested JObject
                        merged[key] = ArrayMerge(mergedObj, (JObject)value);
                    }
                    else
                    {
                        // Directly set or override the value
                        merged[key] = value;
                    }
                }
            }

            return merged;
        }


        /// <summary>
        /// 
        /// </summary>
        /// <param name=""></param>
        /// <returns></returns>
        public async Task<string> MenuHtml(visual_plugins row)
        {
            try
            {
                var config = row.Config.JsonToObj<JObject>();
                var display = config != null && config["Display"]?.ToString() == "0" ? false : true;

                var name = T[$"type_menu.{row.Type}"].Value;
                string mode = row.Mode.Replace("mode_", "");
                string html = $"<div class=\"menu_item {(display ? "" : "item_close")} \" data-fixed-plugins=\"{row.PId}\">";

                if (!new[] { "header", "footer" }.Contains(row.Type))
                {
                    html += "<div class=\"item_move\"></div>";
                }

                html += $"<div class=\"item_icon icon_{row.Type}\"></div>";
                html += $"<div class=\"item_name\" data-name=\"{name}\">{name}</div>";

                if (!new[] { "header", "footer" }.Contains(row.Type))
                {
                    html += $"<div class=\"item_mode\">{name}-{mode}</div>";
                }

                var specialTypes = new[] {"header", "banner", "footer", "product_list", "product_purchase",
                             "combination_purchase", "product_description", "article",
                             "blog_list", "blog_detail"};

                if (!specialTypes.Contains(row.Type))
                {
                    html += "<div class=\"item_display\"></div>";
                }
                else if (new[] { "article", "product_description" }.Contains(row.Type))
                {
                    html += "<div class=\"item_display\"></div>";
                }

                html += "<div class=\"clear\"></div>";
                html += $"<input type=\"hidden\" name=\"PId[]\" value=\"{row.PId}\" />";
                html += $"<input type=\"hidden\" name=\"visual[PId-{row.PId}][Config][Display]\" value=\"{display.ToString().ToLower()}\" />";
                html += "</div>";

                return html;


            }
            catch (Exception ex)
            {
                _logger.LogError(ex, ex.Message);

                return "";
            }


        }


        /// <summary>
        /// 根据插件ID列表批量获取插件
        /// </summary>
        /// <param name="pluginIds">插件ID列表</param>
        /// <returns></returns>
        public async Task<List<visual_plugins>> GetPluginsByIdsAsync(List<int> pluginIds)
        {
            try
            {
                if (pluginIds == null || !pluginIds.Any())
                {
                    return new List<visual_plugins>();
                }

                return await db.Queryable<visual_plugins>()
                    .Where(it => pluginIds.Contains(it.PId))
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "根据插件ID列表批量获取插件失败，PluginIds: {PluginIds}", string.Join(",", pluginIds ?? new List<int>()));
                return new List<visual_plugins>();
            }
        }

    }
}
