using YseStore.Model.Response.Products;
using Entitys;

namespace YseStore.IService.Products
{
    /// <summary>
    /// 可视化产品服务接口
    /// </summary>
    public interface IVisualProductService
    {
        /// <summary>
        /// 根据可视化插件设置获取产品数据
        /// </summary>
        /// <param name="pluginSettings">插件设置JSON字符串</param>
        /// <param name="currentCurrency">当前币种</param>
        /// <param name="userId">用户ID</param>
        /// <returns>可视化产品区块响应</returns>
        Task<VisualProductBlockResponse> GetVisualProductsAsync(string pluginSettings, string currentCurrency = "", int userId = 0);

        /// <summary>
        /// 根据分类ID和标签ID获取产品列表（自动模式）
        /// </summary>
        /// <param name="categoryId">分类ID</param>
        /// <param name="tagIds">标签ID列表</param>
        /// <param name="sortType">排序类型</param>
        /// <param name="number">数量</param>
        /// <param name="currentCurrency">当前币种</param>
        /// <param name="userId">用户ID</param>
        /// <returns>产品列表</returns>
        Task<List<HomeProductItem>> GetProductsByAutoModeAsync(int categoryId, List<int> tagIds, string sortType, int number, string currentCurrency = "", int userId = 0);

        /// <summary>
        /// 根据产品ID列表获取产品列表（手动模式）
        /// </summary>
        /// <param name="productIds">产品ID列表</param>
        /// <param name="currentCurrency">当前币种</param>
        /// <param name="userId">用户ID</param>
        /// <returns>产品列表</returns>
        Task<List<HomeProductItem>> GetProductsByManualModeAsync(List<int> productIds, string currentCurrency = "", int userId = 0);
    }
}
