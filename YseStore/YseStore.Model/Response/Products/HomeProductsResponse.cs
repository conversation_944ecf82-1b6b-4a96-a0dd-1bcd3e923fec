namespace YseStore.Model.Response.Products
{
    /// <summary>
    /// 首页产品数据响应模型
    /// </summary>
    public class HomeProductsResponse
    {
        /// <summary>
        /// 新品推荐产品列表
        /// </summary>
        public List<HomeProductItem> NewArrivalProducts { get; set; } = new List<HomeProductItem>();

        /// <summary>
        /// 导游系统产品列表
        /// </summary>
        public List<HomeProductItem> TourGuideProducts { get; set; } = new List<HomeProductItem>();

        /// <summary>
        /// 寻呼系统产品列表
        /// </summary>
        public List<HomeProductItem> PagingSystemProducts { get; set; } = new List<HomeProductItem>();

        /// <summary>
        /// 呼叫系统产品列表
        /// </summary>
        public List<HomeProductItem> CallSystemProducts { get; set; } = new List<HomeProductItem>();
    }

    /// <summary>
    /// 可视化产品区块响应模型
    /// </summary>
    public class VisualProductBlockResponse
    {
        /// <summary>
        /// 插件ID
        /// </summary>
        public int PId { get; set; }

        /// <summary>
        /// 插件类型
        /// </summary>
        public string Type { get; set; } = string.Empty;

        /// <summary>
        /// 插件模式/风格
        /// </summary>
        public string Mode { get; set; } = string.Empty;

        /// <summary>
        /// 标题
        /// </summary>
        public string Title { get; set; } = string.Empty;

        /// <summary>
        /// 副标题
        /// </summary>
        public string SubTitle { get; set; } = string.Empty;

        /// <summary>
        /// 链接名称
        /// </summary>
        public string LinkName { get; set; } = string.Empty;

        /// <summary>
        /// 链接地址
        /// </summary>
        public string Link { get; set; } = string.Empty;

        /// <summary>
        /// 产品列表
        /// </summary>
        public List<HomeProductItem> Products { get; set; } = new List<HomeProductItem>();
    }

    /// <summary>
    /// 首页产品项模型
    /// </summary>
    public class HomeProductItem
    {
        /// <summary>
        /// 产品ID
        /// </summary>
        public int ProductId { get; set; }

        /// <summary>
        /// 产品名称
        /// </summary>
        public string ProductName { get; set; }

        /// <summary>
        /// 产品URL
        /// </summary>
        public string ProductUrl { get; set; }

        /// <summary>
        /// 主图片路径
        /// </summary>
        public string PrimaryImage { get; set; }

        /// <summary>
        /// 悬停图片路径
        /// </summary>
        public string HoverImage { get; set; }

        /// <summary>
        /// 当前价格
        /// </summary>
        public decimal Price { get; set; }

        /// <summary>
        /// 原价
        /// </summary>
        public decimal? OldPrice { get; set; }

        /// <summary>
        /// 格式化后的当前价格
        /// </summary>
        public string PriceFormat { get; set; }

        /// <summary>
        /// 格式化后的原价
        /// </summary>
        public string OldPriceFormat { get; set; }

        /// <summary>
        /// 产品编码
        /// </summary>
        public string SKU { get; set; }

        /// <summary>
        /// 是否为热门产品
        /// </summary>
        public bool IsHot { get; set; }

        /// <summary>
        /// 是否为新产品
        /// </summary>
        public bool IsNew { get; set; }

        /// <summary>
        /// 产品评分
        /// </summary>
        public decimal Rating { get; set; }

        /// <summary>
        /// 是否已收藏
        /// </summary>
        public bool IsFavorited { get; set; }
    }
}
