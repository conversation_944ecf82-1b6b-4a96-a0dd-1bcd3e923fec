<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup Label="Globals">
    <SccProjectName>SAK</SccProjectName>
    <SccProvider>SAK</SccProvider>
    <SccAuxPath>SAK</SccAuxPath>
    <SccLocalPath>SAK</SccLocalPath>
  </PropertyGroup>

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
    <NoWarn>$(NoWarn);1591</NoWarn>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="MessagePack.Annotations" Version="3.1.3" />
    <PackageReference Include="Mxweb" Version="2.3.15" />
    <PackageReference Include="SqlSugarCore" Version="5.1.4.185" />
	  <PackageReference Include="AutoMapper" Version="12.0.1" />
	  <PackageReference Include="AutoMapper.Extensions.Microsoft.DependencyInjection" Version="12.0.1" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="RequestModels\Visual\" />
  </ItemGroup>

  <ItemGroup>
    <Compile Remove="RequestModels\Visual\ProductsPluginSettings.cs" />
  </ItemGroup>

</Project>
